import { GAME_TYPES } from '../constants/gameTypes';

import { EVENTS as ARENA_EVENTS } from './events/arena';
import { EVENTS as PRACTICE_EVENTS } from './events/practice';
import { EVENTS as FIX_MY_RATING_EVENTS } from './events/fixMyRating';
import { EVENTS as CONTEST_EVENTS } from './events/contest';
import { EVENTS as DAILY_CHALLENGE_EVENTS } from './events/dailyChallenge';
import { EVENTS as SHOWDOWN_EVENTS } from './events/showdown';
import { EVENTS as GAME_EVENTS } from './events/games';
import {
  EVENTS as FRIENDS_EVENTS,
  EVENTS as CLUB_EVENTS,
} from './events/friends';
import { EVENTS as PROFILE_EVENTS } from './events/profile';
import { EVENTS as NAV_EVENTS } from './events/navEvents';
import { EVENTS as LEAGUE_EVENTS } from './events/leagues';
import { EVENTS as LOGIN_EVENTS } from './events/loginEvents';
import { EVENTS as APOLLO_CLIENT_EVENTS } from './events/apolloClient';
import { EVENTS as PUZZLE_EVENTS } from './events/puzzle';
import { EVENTS as PUZZLE_GAME_EVENTS } from './events/puzzleGame';
import { EVENTS as WHATS_NEW_EVENTS } from './events/whatsNew';
import { EVENTS as STREAKS } from './events/streaks';
import { EVENTS as SHARE_VIA_EVENTS } from './events/shareVia';
import { EVENTS as HOME_PAGE_ACTIONS } from './events/homePageActions';
import { EVENTS as RIGHT_PANE_EVENTS } from './events/rightPane';

export const HOST = 'https://us.i.posthog.com';

export const ANALYTICS_EVENTS = {
  UTM_TRACKING: 'utm tracking',
  // ONBOARDING EVENTS
  ONBOARDING: {
    CLICKED_ON_GUEST_LOGIN: 'onboarding: click on guest login',
    CLICKED_ON_GOOGLE_LOGIN: 'onboarding: click on login as existing user',

    VIEWED_FEATURES_PREVIEW_PAGE: 'onboarding: viewed features preview page',
    VIEWED_FEATURE_STORY: 'onboarding: viewed feature story slide',
    REACHED_END_OF_FEATURE_STORIES:
      'onboarding: reached end of feature stories',
    SKIPPED_FEATURE_STORIES: 'onboarding: skipped feature stories',

    VIEWED_DISCOVER_RATING: 'onboarding: view discover rating page',
    CLICKED_DISCOVER_RATING_TAKE_CHALLENGE_NOW:
      'onboarding: click on take challenge now',
    VIEWED_REFERRAL_PAGE: 'onboarding: view referral code page',
  },

  API_SUCCESS: 'api success',
  API_ERROR: 'api error',
  APP_INFINITE_LOADING_ERROR: 'app infinite loading error',

  RESOLUTION: {
    CLICKED_LOGIN: 'clicked on login after resolution',
  },

  WEB_SOCKET: {
    ON_ERROR: 'web socket error',
    ON_ERROR_IN_JSON_PARSE: 'web socket error in json parse',
  },
  COMPONENT_LOAD_ERROR:{
    ON_ERROR: 'component load error',
  },

  ...APOLLO_CLIENT_EVENTS,

  ...ARENA_EVENTS,
  ...PRACTICE_EVENTS,
  ...FIX_MY_RATING_EVENTS,
  ...CONTEST_EVENTS,
  ...DAILY_CHALLENGE_EVENTS,
  ...SHOWDOWN_EVENTS,
  ...GAME_EVENTS,
  ...FRIENDS_EVENTS,
  ...CLUB_EVENTS,
  ...PROFILE_EVENTS,
  ...NAV_EVENTS,
  ...LEAGUE_EVENTS,
  ...LOGIN_EVENTS,
  ...PUZZLE_EVENTS,
  ...WHATS_NEW_EVENTS,
  ...STREAKS,
  ...SHARE_VIA_EVENTS,
  ...HOME_PAGE_ACTIONS,
  ...RIGHT_PANE_EVENTS,

  GAME: {
    CLICK_ON_CREATE_GAME: 'click on create game',
    CLICKED_ON_GAME_DETAILED_ANALYSIS: 'click on game detailed analysis',
  },

  PUZZLE_GAME: {
    ...PUZZLE_GAME_EVENTS,
  },

  NET_ISSUE: {
    MATIKS_COM_NOT_RECHABLE: 'net-issue: matiks.com is not reachable',
    EXPO_UPDATES_NOT_REACHABLE: 'net-issue: expo-updates server not reachable',
    USER_CLICKED_ON_FEEDBACK_FORM: 'net-issue: user clicked on feedback form',
  },

  BADGE: {
    BADGE_EARNED_MODAL_SHOWN: 'badge earned modal shown',
    CLICKED_ON_BADGE: 'clicked on badge',
  },
  STATIK_COINS: {
    CLICKED_ON_STATIK_COINS: 'click on statik coins card',
  },

  // GAME-COUNTDOWN
  GAME_COUNTDOWN: {
    CLICKED_ON_GAME_COUNTDOWN_LEAVE_GAME: 'click on game counter leave game',
    CLICKED_ON_GAME_COUNTDOWN_BACK: 'click on game counter back ',
  },

  // LEADERBOARD
  LEADERBOARD: {
    VIEWED: 'view leaderboard',
  },

  STATIK_COINS_LEADERBOARD: {
    VIEWED: 'view statik coins leaderboard',
  },

  // SEARCHING_FOR_MATHLETE
  SEARCHING_FOR_MATHLETE: {
    VIEWED: 'view searching for mathlete',
    CLICKED_ON_ABORT_SEARCH: 'click on searching mathlete abort search',
  },
  SEARCHING_FOR_MATHLETE_PUZZLE_GAME: {
    VIEWED: 'view searching for mathlete puzzle game',
    CLICKED_ON_ABORT_SEARCH:
      'click on searching mathlete puzzle game abort search',
  },

  CLICKED_ON_DOWNLOAD_APP_NOW_BUTTON: 'click on download app now button',
  CLICKED_ON_DOWNLOAD_APP_NOW_BUTTON_FROM_BOTTOM_SHEET:
    'click on download app now button from bottom sheet',

  CLICKED_ON_GO_BACK_FROM_GAME: 'click on game go back',

  CLICK_ON_PAGE_HEADER_LOGOUT: 'click on page header logout',
  CLICK_ON_PAGE_HEADER_PROFILE: 'click on page header profile',
  CLICK_ON_PAGE_HEADER_SETTINGS: 'click on page header settings',
  // Navbar
  CLICKED_ON_MATIKS_LOGO_IN_NAV_HEADER: 'Clicked on Nav Matiks logo',

  // pages
  VISITED_PUBLIC_LANDING_PAGE: 'visited public landing page',
  VISITED_LOGIN_PAGE: 'visited login page',

  CLICKED_ON_PLAY_NOW: 'click on public home play now',

  // play with friend
  USER_JOINED: 'User Joined',
  JOIN_GAME: 'joined play with friend game',

  CLICK_START_GAME: 'click on play with friend start game',

  // share
  SHARE_RESULT: 'Share Result',
  RESULT_PAGE_SHOWN: 'view game result modal',

  CLICKED_ON_PROFILE_ICON: 'Clicked on Profile Icon',
  SEARCH_PLAYER_ON_LEADERBOARD: 'Searched Player on Leaderboard',

  SEARCH_USER_ON_FRIENDS_PAGE: 'Search User on Friends Page',

  WEEKLY_LEAGUE: 'weekly league',
};

export const GAME_TYPE = 'Game Type';

export const GAMES_PROPERTIES = {
  [GAME_TYPES.PRACTICE]: 'Practice',
  [GAME_TYPES.PLAY_ONLINE]: 'Online Duels',
  [GAME_TYPES.PLAY_WITH_FRIEND]: 'Play With Friend',
  [GAME_TYPES.GROUP_PLAY]: 'Group Play',
};
